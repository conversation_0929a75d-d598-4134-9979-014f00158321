@import "../../template/null/null.scss";

// 全局滚动条隐藏 - 针对 iOS 设备
::-webkit-scrollbar {
  display: none !important;
  width: 0 !important;
  height: 0 !important;
  background: transparent !important;
  -webkit-appearance: none !important;
}

// 针对 scroll-view 组件的特殊处理
scroll-view {
  ::-webkit-scrollbar {
    display: none !important;
    width: 0 !important;
    height: 0 !important;
  }
}

.immersive-navbar-container {
  position: relative;
  width: 100%;
  height: 100vh;
  // 确保容器不会产生额外的滚动条
  overflow: hidden;
  // 设置背景色，防止下拉时露出白色
  background-color: #e72410; // 使用导航栏的背景色
}

.immersive-navbar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 100;
  transition: background-color 0.2s ease;
  overflow: hidden;
  /* 初始完全透明 */
  background-color: transparent;
  z-index: 100;
}

.navbar-back {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  line-height: 1;
  z-index: 20;
}

.back-icon {
  position: absolute;
  width: 36rpx;
  height: 36rpx;
  z-index: 11;

  image {
    width: 100%;
    height: 100%;
  }
}

.navbar-title {
  width: 100%;
  box-sizing: border-box;
  font-weight: 600;
  font-size: 34rpx;
  /* 颜色通过JS动态设置，不在CSS中固定 */
  display: flex;
  align-items: center;
  position: absolute;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  /* 透明度通过JS动态设置，添加过渡动画 */
  transition: opacity 0.2s ease;
}

.page-content {
  position: relative;
  width: 100%;
  height: 100vh;
  box-sizing: border-box;
  // 设置背景色，防止下拉时露出白色
  background-color: #f7f7f7;

  // 去掉滚动条 - 增强版本，兼容更多设备
  ::-webkit-scrollbar {
    display: none !important;
    width: 0 !important;
    height: 0 !important;
    color: transparent !important;
    background: transparent !important;
    -webkit-appearance: none !important;
  }

  // 兼容其他浏览器内核
  scrollbar-width: none !important; /* Firefox */
  -ms-overflow-style: none !important; /* IE and Edge */

  // 确保没有边框和轮廓
  border: none !important;
  outline: none !important;

  // 防止内容溢出到右侧
  overflow-x: hidden !important;

  // iOS 特殊处理
  -webkit-overflow-scrolling: touch !important;

  // 强制隐藏滚动条的额外样式
  &::-webkit-scrollbar {
    display: none !important;
    width: 0 !important;
    height: 0 !important;
    background: transparent !important;
  }

  &::-webkit-scrollbar-track {
    display: none !important;
    background: transparent !important;
  }

  &::-webkit-scrollbar-thumb {
    display: none !important;
    background: transparent !important;
  }
}

// 底部加载状态样式
.loading-footer {
  padding: 40rpx 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #f7f7f7;

  .loading-content {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: row;

    .loading-icon {
      width: 32rpx;
      height: 32rpx;
      border: 4rpx solid #e0e0e0;
      border-top: 4rpx solid #e72410;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-right: 16rpx;
    }

    .loading-text {
      font-size: 28rpx;
      color: #74798c;
      font-weight: 400;
    }
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
