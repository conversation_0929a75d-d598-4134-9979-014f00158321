// 引入ConfigurableForm的数据处理工具
import {
  handleMultiple,
  handleData,
  getNameFromPop
} from '../../../components/ConfigurableForm/utils/data-processor';

const app = getApp();

Page({
  data: {
    purchased: true, // 默认购买
    externalFilterState: {}, // 企业名单回显
    showMapMode: false, // 不显示地图模式
    dropDownMenuConfig: ['region', 'industry', 'filter'], // 下拉菜单配置
    vipVisible: false // VIP弹窗显示状态
  },

  onLoad(options) {
    let heightParams = options?.str;
    let tempExternalFilterState = {
      filterParams: {}
    };

    // 说明是从高级搜索那边过来的
    if (heightParams) {
      heightParams = JSON.parse(decodeURIComponent(heightParams));
      
      // 处理地区数据
      if (heightParams.area_code_list?.length > 0) {
        tempExternalFilterState['regionData'] = {
          code: heightParams.area_code_list[0]?.code || '',
          name: heightParams.area_code_list[0]?.name || ''
        };
      }

      // 处理产业数据
      if (heightParams.industry_code_list?.length > 0) {
        tempExternalFilterState['industrial_list'] = {
          code: heightParams.industry_code_list[0]?.code || '',
          name: heightParams.industry_code_list[0]?.name || ''
        };
      }

      // 处理其他筛选参数
      tempExternalFilterState['filterParams'] = {
        ...heightParams,
        ent_entity_type: ['1'] // 确保只搜索企业
      };

      // 移除已经处理过的字段，避免重复
      delete tempExternalFilterState.filterParams.area_code_list;
      delete tempExternalFilterState.filterParams.industry_code_list;
    }

    this.setData({
      externalFilterState: tempExternalFilterState
    });
  },

  // VIP弹窗处理
  vipPop(val) {
    this.setData({
      vipVisible: val
    });
  }
});
