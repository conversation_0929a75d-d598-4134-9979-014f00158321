// 引入ConfigurableForm的数据处理工具
import {
  handleMultiple,
  handleData,
  getNameFromPop
} from '../../../components/ConfigurableForm/utils/data-processor';

const app = getApp();

Page({
  data: {
    purchased: true, // 默认购买
    externalFilterState: {}, // 企业名单回显
    showMapMode: false, // 不显示地图模式
    dropDownMenuConfig: ['region', 'industry', 'filter'], // 下拉菜单配置
    vipVisible: false // VIP弹窗显示状态
  },

  onLoad(options) {
    let heightParams = options?.str;
    let tempExternalFilterState = {
      filterParams: {}
    };

    // 说明是从高级搜索那边过来的
    if (heightParams) {
      heightParams = JSON.parse(decodeURIComponent(heightParams));
      console.log('原始参数:', heightParams);

      // 处理地区数据 - 检查多种可能的数据格式
      if (heightParams.area_code_list?.length > 0) {
        const areaItem = heightParams.area_code_list[0];
        // 处理可能的数据格式：直接对象 或 包含选中状态的对象
        const areaData =
          areaItem.status === 'checked' || areaItem.active === true
            ? areaItem
            : typeof areaItem === 'object' && areaItem.code
            ? areaItem
            : null;

        if (areaData && areaData.code) {
          tempExternalFilterState['regionData'] = {
            code: areaData.code,
            name: areaData.name || ''
          };
        }
      }

      // 处理产业数据 - 检查多种可能的数据格式
      if (heightParams.industry_code_list?.length > 0) {
        const industryItem = heightParams.industry_code_list[0];
        // 处理可能的数据格式
        const industryData =
          industryItem.status === 'checked' || industryItem.active === true
            ? industryItem
            : typeof industryItem === 'object' && industryItem.code
            ? industryItem
            : null;

        if (industryData && industryData.code) {
          // 根据 tabCode 或 type 判断产业类型
          if (
            industryData.tabCode === 'classic' ||
            industryData.type === 'classic'
          ) {
            tempExternalFilterState['classic_industry_code_list'] = {
              code: industryData.code,
              name: industryData.name || ''
            };
          } else {
            // 默认为热点产业或产业链图谱
            tempExternalFilterState['industrial_list'] = {
              code: industryData.code,
              name: industryData.name || ''
            };
          }
        }
      }

      // 处理其他筛选参数 - 使用 handleData 处理
      const rawFilterParams = {...heightParams};

      // 移除已经处理过的字段，避免重复
      delete rawFilterParams.area_code_list;
      delete rawFilterParams.industry_code_list;

      // 使用 handleData 处理参数格式
      const processedFilterParams = handleData(rawFilterParams, {
        returnString: false
      });

      tempExternalFilterState['filterParams'] = processedFilterParams;

      console.log('转换后的参数:', tempExternalFilterState);
    }

    this.setData({
      externalFilterState: tempExternalFilterState
    });
  },

  // VIP弹窗处理
  vipPop(val) {
    this.setData({
      vipVisible: val
    });
  }
});
