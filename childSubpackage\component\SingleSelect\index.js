import {
  hotIndustryTypeSelectorApi, // 热点
  classicIndustryExpandListApi, // 经典
  originalIndustryClusterSelectorApi, // 产业图谱
  originalIndustryPurchasedListApi // 产业链图谱 - 已购买的产业链
} from '../../../service/industryApi';

// Tab 选项配置
const TAB_OPTIONS = [
  {
    code: 'hot',
    name: '热点产业名单'
  },
  {
    code: 'classic',
    name: '经典产业名单'
  },
  {
    code: 'emerging',
    name: '产业链图谱'
  }
];

// 移除全局变量，数据将存储在组件的 data 中
const app = getApp();

Component({
  properties: {
    visible: {
      type: Boolean,
      value: false
    },
    position: {
      type: String,
      value: 'top'
    },
    startDistance: {
      type: String,
      value: '0px'
    },
    defaultCode: {
      type: null,
      value: ''
    },
    top: {
      type: Number,
      value: 0
    },
    zIndex: {
      type: Number,
      value: 10
    }
  },

  data: {
    parentList: [],
    activeChildList: [],
    selectedPath: {
      parent: null,
      child: null
    },
    finalSelection: null,
    readyToShow: false,
    tabs: TAB_OPTIONS,
    activeTabIndex: 0, // 默认选中第一个tab（热点产业名单）
    currentDataSource: [],
    // 数据源映射
    dataSourceMap: {
      hot: [],
      classic: [],
      emerging: []
    },
    // 状态记忆：只记录已确认的选择（点击第二级时确认）
    confirmedSelection: {
      tabIndex: 0, // 已确认选择的Tab索引
      selectedCode: null, // 已确认选择的code
      hasConfirmed: false // 是否有已确认的选择
    }
  },

  lifetimes: {
    async attached() {
      this.setData({readyToShow: false});

      // 初始化数据源 - 从 API 获取真实数据
      await this.initializeDataSources();

      // 使用默认数据源初始化组件
      this.initializeData(this.data.currentDataSource);
    }
  },

  observers: {
    visible: function (bl) {
      if (bl) {
        const {confirmedSelection, defaultCode} = this.data;

        // 确保数据已初始化
        if (this.data.parentList.length === 0) {
          this.initializeData(this.data.currentDataSource);
        }

        // 如果有已确认的选择，恢复到该状态
        if (confirmedSelection.hasConfirmed) {
          this.setData({
            activeTabIndex: confirmedSelection.tabIndex
          });
          this.switchTab({
            currentTarget: {
              dataset: {
                index: confirmedSelection.tabIndex
              }
            }
          });
        } else if (defaultCode && this.data.dataSourceMap) {
          // 如果有 defaultCode 且数据已初始化，处理回显
          this.setData({readyToShow: false});
          const extractedCode = this.extractCodeFromDefaultCode(defaultCode);
          if (extractedCode) {
            this.setDefaultSelectWithTab(extractedCode);
          } else {
            this.setData({readyToShow: true});
          }
        }
      }
    },

    // 监听 defaultCode 变化，用于回显选中状态
    defaultCode: function (code) {
      if (code && this.data.visible && this.data.dataSourceMap) {
        this.setData({readyToShow: false});
        const extractedCode = this.extractCodeFromDefaultCode(code);
        // 只有当提取出有效的code时才设置默认选择
        if (extractedCode) {
          this.setDefaultSelectWithTab(extractedCode);
        } else {
          // 没有有效code时直接显示
          this.setData({readyToShow: true});
        }
      }
    }
  },

  methods: {
    // 初始化数据源 - 从 API 获取真实数据
    async initializeDataSources() {
      try {
        // 并行获取三个数据源
        const [hotData, classicData, emergingData] = await Promise.all([
          this.getHotIndustryData(),
          this.getClassicIndustryData(),
          this.getEmergingIndustryData()
        ]);
        // console.log(22222222222, hotData);

        this.setData({
          'dataSourceMap.hot': hotData,
          'dataSourceMap.classic': classicData,
          'dataSourceMap.emerging': emergingData,
          currentDataSource: hotData // 默认使用热点产业数据
        });
      } catch (error) {
        console.error('初始化数据源失败:', error);
        // 使用空数据作为fallback
        this.setData({
          'dataSourceMap.hot': [],
          'dataSourceMap.classic': [],
          'dataSourceMap.emerging': [],
          currentDataSource: []
        });
      }
    },

    // 获取热点产业数据
    async getHotIndustryData() {
      try {
        const response = await hotIndustryTypeSelectorApi();
        return response || [];
      } catch (error) {
        console.error('获取热点产业数据失败:', error);
        return [];
      }
    },

    // 获取经典产业数据
    async getClassicIndustryData() {
      try {
        const response = await classicIndustryExpandListApi();
        return response || [];
      } catch (error) {
        console.error('获取经典产业数据失败:', error);
        return [];
      }
    },

    // 获取产业链图谱数据
    async getEmergingIndustryData() {
      try {
        // 先获取已购买的产业链列表
        const purchasedList = await originalIndustryPurchasedListApi();
        const purchasedCodes = new Set(purchasedList.map(item => item.code));

        // 获取完整的产业链图谱数据
        const response = await originalIndustryClusterSelectorApi();

        // 标记已购买状态
        const dataWithPurchaseStatus = (response || []).map(parent => ({
          ...parent,
          children: (parent.children || []).map(child => ({
            ...child,
            purchased: purchasedCodes.has(child.code)
          }))
        }));

        return dataWithPurchaseStatus;
      } catch (error) {
        console.error('获取产业链图谱数据失败:', error);
        return [];
      }
    },

    // 初始化数据
    initializeData(dataSource) {
      if (!dataSource || dataSource.length === 0) {
        this.setData({
          parentList: [],
          activeChildList: [],
          readyToShow: true
        });
        return;
      }

      // 处理父级列表
      const parentList = dataSource.map((item, index) => ({
        ...item,
        active: index === 0,
        selected: false
      }));

      // 处理子级列表（默认显示第一个父级的子项）
      const activeChildList = dataSource[0]?.children || [];

      this.setData({
        parentList,
        activeChildList,
        readyToShow: true
      });
    },

    // Tab 切换
    switchTab(e) {
      const index = e.currentTarget.dataset.index;
      const tabCode = this.data.tabs[index].code;
      const newDataSource = this.data.dataSourceMap[tabCode];

      this.setData({
        activeTabIndex: index,
        currentDataSource: newDataSource
      });

      // 重新初始化数据
      this.initializeData(newDataSource);
    },

    // 选择父级
    selectParent(e) {
      const {code, name, index} = e.currentTarget.dataset;
      const {parentList, currentDataSource} = this.data;

      // 更新父级列表的激活状态
      const updatedParentList = parentList.map((item, idx) => ({
        ...item,
        active: idx === index
      }));

      // 获取对应的子级列表
      const selectedParent = currentDataSource.find(item => item.code === code);
      const activeChildList = selectedParent?.children || [];

      this.setData({
        parentList: updatedParentList,
        activeChildList,
        'selectedPath.parent': {code, name}
      });
    },

    // 选择子级
    selectChild(e) {
      const {code, name, purchased} = e.currentTarget.dataset;
      const {activeTabIndex} = this.data;

      // 如果是产业链图谱且未购买，触发VIP提示
      if (activeTabIndex === 2 && !purchased) {
        this.triggerEvent('vip');
        return;
      }

      // 记录已确认的选择
      this.setData({
        'confirmedSelection.tabIndex': activeTabIndex,
        'confirmedSelection.selectedCode': code,
        'confirmedSelection.hasConfirmed': true
      });

      // 触发选择事件
      this.triggerEvent('submit', {code, name});

      // 关闭弹窗
      this.close();
    },

    // 从 defaultCode 中提取有效的 code
    extractCodeFromDefaultCode(defaultCode) {
      if (!defaultCode) return null;

      // 如果是字符串，直接返回
      if (typeof defaultCode === 'string') {
        return defaultCode;
      }

      // 如果是对象，提取 code 属性
      if (typeof defaultCode === 'object' && defaultCode.code) {
        return defaultCode.code;
      }

      return null;
    },

    // 根据 code 设置默认选择并切换到对应的 tab
    setDefaultSelectWithTab(code) {
      const {dataSourceMap} = this.data;

      // 遍历所有数据源，找到包含该 code 的数据源
      for (let tabIndex = 0; tabIndex < dataSourceMap.length; tabIndex++) {
        const dataSource = dataSourceMap[tabIndex];

        // 在当前数据源中查找匹配的项
        const foundItem = this.findItemInDataSource(dataSource, code);

        if (foundItem) {
          // 找到了，设置选中状态并切换到对应的 tab
          this.setData({
            activeTabIndex: tabIndex,
            confirmedSelection: {
              hasConfirmed: true,
              tabIndex: tabIndex,
              selectedItem: foundItem
            }
          });

          // 切换到对应的 tab
          this.switchTab({
            currentTarget: {
              dataset: {
                index: tabIndex
              }
            }
          });

          // 设置选中状态
          this.setSelectedState(foundItem, tabIndex);

          this.setData({readyToShow: true});
          return;
        }
      }

      // 没找到匹配项，直接显示
      this.setData({readyToShow: true});
    },

    // 在数据源中查找指定 code 的项
    findItemInDataSource(dataSource, code) {
      for (const parentItem of dataSource) {
        // 检查父级
        if (parentItem.code === code) {
          return {
            parent: {code: parentItem.code, name: parentItem.name},
            child: null
          };
        }

        // 检查子级
        if (parentItem.children) {
          for (const childItem of parentItem.children) {
            if (childItem.code === code) {
              return {
                parent: {code: parentItem.code, name: parentItem.name},
                child: {code: childItem.code, name: childItem.name}
              };
            }
          }
        }
      }
      return null;
    },

    // 设置选中状态
    setSelectedState(foundItem, tabIndex) {
      const {parent, child} = foundItem;

      // 设置选中路径
      this.setData({
        'selectedPath.parent': parent,
        'selectedPath.child': child
      });

      // 如果有子级选择，需要更新父级列表和子级列表的状态
      if (child) {
        const currentDataSource = this.data.dataSourceMap[tabIndex];
        const parentIndex = currentDataSource.findIndex(
          item => item.code === parent.code
        );

        if (parentIndex !== -1) {
          // 更新父级列表状态
          const updatedParentList = this.data.parentList.map((item, idx) => ({
            ...item,
            active: idx === parentIndex
          }));

          // 获取子级列表
          const activeChildList = currentDataSource[parentIndex].children || [];

          // 更新子级列表状态
          const updatedChildList = activeChildList.map(item => ({
            ...item,
            active: item.code === child.code
          }));

          this.setData({
            parentList: updatedParentList,
            activeChildList: updatedChildList
          });
        }
      }
    },

    // 关闭弹窗
    close() {
      this.triggerEvent('close');
    }
  }
});
