/* components/SingleSelect/index.scss */

/* 主容器样式 */
.area {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

/* Tab 头部样式 */
.tab-header {
  position: relative;
  display: flex;
  background: #fff;
  height: 84rpx;
  z-index: 1;
  box-sizing: border-box;
  width: 100%;
  // 1rpx下边框
  &::before {
    content: " ";
    width: 100%;
    height: 1rpx;
    background: #eee;
    position: absolute;
    bottom: 0;
    left: 0;
    transform: scaleY(0.8);

    // transform: scaleY(0.5);
  }
  &::after {
    content: " ";
    width: 100%;
    height: 1rpx;
    background: #eee;
    position: absolute;
    top: 0;
    left: 0;
    transform: scaleY(0.8);
    // transform: scaleY(0.5);
  }
}

.tab-item {
  position: relative;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  .tab-text {
    font-size: 28rpx;
    color: #74798c;
    transition: all 0.3s ease;
  }
  &.active .tab-text {
    font-weight: 600;
    font-size: 28rpx;
    color: #e72410;
  }
  .tab-line {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 40rpx;
    height: 6rpx;
    background: linear-gradient(90deg, #e72410 0%, #f17b6f 100%);
    opacity: 0;
    transition: opacity 0.3s ease;

    &.show {
      opacity: 1;
    }
  }
}

/* 内容容器样式 */
.select-container {
  width: 100%;
  position: relative;
  overflow: hidden;
  height: 676rpx; /* 总高度760rpx - Tab头部84rpx = 676rpx */
  display: flex;
  flex-direction: column;
}

.select-wrap {
  height: 100%;
  width: 100%;
  display: flex;
  font-weight: 400;
  font-size: 26rpx;
  color: #525665;
  flex: 1; /* 占满父容器高度 */
}

.select-wrap .list {
  height: 100%;
  overflow-y: auto;
}

.select-wrap .list .item {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 26rpx 24rpx;
  box-sizing: border-box;
}

.parent-list {
  background-color: rgba(247, 247, 247, 0.6);
  width: 38.4% !important; /* 288rpx / 750rpx = 38.4% */
  flex-shrink: 0;
  height: 100%;
}

.parent-list .item.active {
  background-color: #fff;
}

.parent-list .item.selected {
  background-color: #fff;
  font-weight: 600;
  font-size: 26rpx;
  color: #e72410;
}

.child-list {
  background-color: #fff;
  flex: 1;
  position: relative;

  /* 左边框分隔线 */
  // &::before {
  //   content: " ";
  //   position: absolute;
  //   left: 0;
  //   top: 0;
  //   width: 1px;
  //   height: 100%;
  //   background: #eeeeee;
  //   transform: scaleX(0.5);
  // }
}

.child-list .item.selected {
  font-weight: 600;
  font-size: 26rpx;
  color: #e72410;
}

.selected-text {
  color: #e72410 !important;
  font-size: 26rpx;
  font-weight: 600 !important;
}

.checkmark {
  width: 32rpx; /* 28rpx / 750rpx = 3.73% */
  height: 32rpx; /* 高度保持固定，避免变形 */
  flex-shrink: 0; /* 防止图标被压缩 */
}

.checkmark.show {
  opacity: 1;
}

.placeholder-content {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.placeholder-text {
  font-size: 28rpx;
  color: #999;
}
