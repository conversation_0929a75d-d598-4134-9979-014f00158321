<!--companyPackage/pages/industryChain/chainList/component/ChainSelect/ChainSelect.wxml-->
<HalfScreenPop 
  showFooter="{{false}}" 
  disableAnimation="{{true}}"
  visible="{{visible}}" 
  position="{{position}}" 
  zIndex="{{10}}" 
>
  <view slot="customContent">
    <view class="chain-wrap">
      <!-- 父级列表 -->
      <scroll-view scroll-y class="parent">
        <view 
          wx:for="{{allData}}" 
          wx:key="code"
          data-select="{{item}}"
          data-parentind="{{index}}"
          bindtap="handleChange"
          class="item {{item.active && 'active'}}"
        >
          <view class="name text-ellipsis">{{item.name}}</view>
        </view>
      </scroll-view>
      <!-- 子级列表 -->
      <scroll-view scroll-y class="children">
        <view 
          wx:for="{{activeChildList}}"
          wx:for-item="child"
          wx:key="name"
          data-select="{{child}}"
          bindtap="handleChange"
          class="item text-ellipsis {{child.active && 'active'}}"
        >
          {{child.name}}
        </view>
      </scroll-view>
    </view>
  </view>
</HalfScreenPop>
