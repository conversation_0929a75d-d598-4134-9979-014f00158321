<import src="/template/null/null"></import>
<view class="immersive-navbar-container">
  <!-- 固定导航栏 -->
  <view class="immersive-navbar" style="{{navbarStyle}}">
    <!-- 返回按钮 -->
    <view
      wx:if="{{showBackBtn}}"
      class="navbar-back"
      style="{{backBtnStyle}}"
      catchtap="handleBack"
    >
      <view class="back-icon" style="{{backIconStyle}}">
        <image
          src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_black_arrow.png"
        ></image>
      </view>
    </view>

    <!-- 标题 -->
    <view
      wx:if="{{title}}"
      class="navbar-title nav_title"
      style="{{titleStyle}}"
    >
      {{title}}
    </view>
  </view>

  <!-- 页面内容区域 -->
  <scroll-view
    class="page-content"
    style="{{contentStyle}}"
    scroll-y="{{true}}"
    bindscroll="onScroll"
    bindscrolltolower="onScrollToLower"
    bounces="{{false}}"
    enhanced="{{false}}"
    lower-threshold="{{50}}"
    show-scrollbar="{{false}}"
    enable-passive="{{false}}"
    scroll-with-animation="{{false}}"
    refresher-enabled="{{true}}"
    refresher-background="{{bgColor}}"
    refresher-default-style="none"
    refresher-triggered="{{false}}"
  >
    <slot name="content"></slot>
    <view style="height: 500rpx;" wx:if="{{loadingState==='empty'}}">
      <template is="null"></template>
    </view>

    <!-- 底部加载状态 -->
    <view class="loading-footer" wx:if="{{showLoadingFooter}}">
      <view class="loading-content" wx:if="{{loadingState === 'loading'}}">
        <!-- <view class="loading-icon"></view> -->
        <text class="loading-text">加载中...</text>
      </view>
      <view class="loading-content" wx:if="{{loadingState === 'nomore'}}">
        <text class="loading-text">已加载全部内容</text>
      </view>
    </view>
  </scroll-view>
</view>
