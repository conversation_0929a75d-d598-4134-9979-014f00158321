import {dataHelpers} from '../../../../components/ConfigurableForm/utils/helpers';
import {
  clearChildComponent,
  fillChildComponent,
  getChildComponentHasVal,
  handleData
} from '../../../../components/ConfigurableForm/utils/component-helpers';

// 地区名称处理函数（来自 components/hunt/mixin.js）
const getNameFromPop = function (data) {
  if (data?.length > 0 && data?.[0]?.MultipleCelectionSingleSelection) {
    //产业链单独处理
    return data[0].name;
  }
  let arr = [];
  data?.length > 0 &&
    data.forEach((i, idx, oldArr) => {
      if (i.status == 'checked') {
        if (i.parent && i.parent != '') {
          // 有父亲
          let parent = oldArr.filter(itm => i.parent == itm.code)[0];
          parent.status == 'checked' ? arr.push(parent) : arr.push(i);
        } else {
          // 没有父亲
          arr.push(i);
        }
      }
    });
  // 排重
  let newobj = {};
  arr = arr.reduce((preVal, curVal) => {
    newobj[curVal.code] ? '' : (newobj[curVal.code] = preVal.push(curVal));
    return preVal;
  }, []);
  return arr.map(item => item.name).join('');
};
var behavior = require('../../../../template/menuhead/index');

const app = getApp();
Component({
  behaviors: [behavior],
  properties: {
    // 下拉菜单标题
    dropDownMenuTitle: {
      type: Array,
      value: ['全国', '产业类型', '更多筛选']
    },

    // 下拉菜单配置，用于动态决定每个位置显示什么功能
    dropDownMenuConfig: {
      type: Array,
      value: ['region', 'industry', 'filter'], // 默认配置：地区、产业链、更多筛选
      observer() {
        this.updateMenuConfig();
      }
    },

    // 外部筛选状态（用于回显）
    externalFilterState: {
      type: Object,
      value: null,
      observer(newVal) {
        // 只有在组件初始化完成后才执行
        if (
          this.data.isInitialized &&
          Object.keys(newVal)?.length &&
          !this.data.isBackfilling
        ) {
          this.applyExternalState(newVal);
        }
      }
    }
  },
  data: {
    isIphoneX: app.globalData.isIphoneX,
    // 筛选相关
    regionData: {}, //选中的地区
    chainData: {}, //选中的产业链
    moreFilterParams: {}, // 更多筛选参数
    temParams: {}, //更多筛选改变的时候赋值
    allChainData: [],
    selectChainVal: '',
    isBackfilling: false, // 是否正在回填数据的标志

    // 动态菜单配置
    menuConfig: {
      region: {position: 0, enabled: true},
      industry: {position: 1, enabled: true},
      filter: {position: 2, enabled: true}
    },
    isInitialized: false // 添加初始化标志
  },

  observers: {
    regionData: function (obj) {
      if (obj?.code && obj.code != 'All') {
        this.setData({
          district_val: true
        });
      }
    },
    chainData: function (obj) {
      if (obj.code) {
        this.setData({
          source_val: true
        });
      }
    },
    filter_open: function (val) {
      if (!val) return;
      // 回填更多筛选
      clearChildComponent(this, '#s-hunt')();

      // 延迟回填，确保 ConfigurableForm 组件已经渲染完成
      setTimeout(() => {
        const moreFilterParams = this.data.moreFilterParams;
        if (moreFilterParams && Object.keys(moreFilterParams).length > 0) {
          fillChildComponent(
            this,
            '#s-hunt'
          )(dataHelpers.deepClone(moreFilterParams));
        }
      }, 100);
    }
  },

  lifetimes: {
    attached: function () {
      // 在组件实例进入页面节点树时执行
      this.updateMenuConfig();
      this.setData({isInitialized: true});

      // 检查是否有外部筛选状态需要应用
      if (
        this.properties.externalFilterState &&
        Object.keys(this.properties.externalFilterState).length
      ) {
        this.applyExternalState(this.properties.externalFilterState);
      }
    }
  },

  methods: {
    // 更新菜单配置
    updateMenuConfig() {
      const {dropDownMenuConfig} = this.properties;

      const menuConfig = {
        region: {position: -1, enabled: false},
        industry: {position: -1, enabled: false},
        filter: {position: -1, enabled: false}
      };

      // 根据配置数组设置每个功能的位置
      dropDownMenuConfig.forEach((type, index) => {
        if (menuConfig[type]) {
          menuConfig[type] = {position: index, enabled: true};
        }
      });

      this.setData({menuConfig});
    },

    // 地区
    getRegion(e) {
      // 多选地区组件返回的是 checkedList 数组
      const checkedList = e.detail;
      let {dropDownMenuTitle} = this.properties;
      let newDropDownMenuTitle = [...dropDownMenuTitle];

      if (!checkedList || checkedList.length === 0) {
        // 没有选择任何地区，重置为全国
        this.setData({
          regionData: {},
          regionDataList: [],
          district_val: false
        });
        newDropDownMenuTitle[0] = '全国';
      } else {
        // 有选择地区
        this.setData({
          regionDataList: checkedList,
          regionData: checkedList[0], // 兼容性：设置第一个地区
          district_val: true
        });

        // 更新菜单标题 - 使用原来的地区名称显示逻辑
        const regionName = getNameFromPop(checkedList);
        newDropDownMenuTitle[0] = regionName || '全国';
      }

      this.setData(
        {
          dropDownMenuTitle: newDropDownMenuTitle
        },
        () => this.backAll()
      );
    },

    // 选中产业链
    handleSelectChain({detail}) {
      const {selection} = detail;
      this.setData(
        {
          chainData: selection,
          selectChainVal: selection.code
        },
        () => this.backAll()
      );
      this.closeHyFilter();
    },

    closeRegion() {
      this.closeHyFilter();
    },

    // ConfigurableForm改变
    onConfigurableFormSubmit(e) {
      const {paramsData} = e.detail;
      const clonedData = dataHelpers.deepClone(paramsData);
      this.setData({
        temParams: clonedData
      });
    },

    // ConfigurableForm VIP事件
    onConfigurableFormVip(e) {
      this.triggerEvent('vip', e.detail);
    },

    // ConfigurableForm 初始化完成
    onConfigurableFormReady() {
      // 如果有待回填的数据，现在进行回填
      if (
        this.data.moreFilterParams &&
        Object.keys(this.data.moreFilterParams).length > 0
      ) {
        const configurableForm = this.selectComponent('#s-hunt');
        if (configurableForm && configurableForm.setBackfillData) {
          configurableForm.setBackfillData(this.data.moreFilterParams);
        }
      }
    },

    // 确定更多筛选
    confirmMoreFilter() {
      const {temParams} = this.data;
      const clonedParams = dataHelpers.deepClone(temParams);
      this.setData(
        {
          moreFilterParams: clonedParams
        },
        () => {
          this.backAll(true);
        }
      );
    },

    resetMoreFilter() {
      clearChildComponent(this, '#s-hunt')();
      this.setData({
        temParams: {}
      });
    },

    // 总的返回 - 支持更多筛选参数
    backAll(type = false) {
      const {regionData, regionDataList, chainData, moreFilterParams} =
        this.data;

      // 处理地区数据：优先使用多选数据，兼容单选数据
      let areaCodeList = [];
      let regionName = '全国';

      if (regionDataList && regionDataList.length > 0) {
        // 多选地区的情况
        areaCodeList = regionDataList
          .map(item => item.code)
          .filter(code => code && code !== 'All');
        // 使用原来的地区名称显示逻辑
        regionName = getNameFromPop(regionDataList) || '全国';
      } else if (regionData?.code && regionData.code !== 'All') {
        // 单选地区的情况（兼容性）
        areaCodeList = [regionData.code];
        regionName = regionData.name || '全国';
      }

      const requestData = {
        name1: regionName,
        name2: chainData?.name || '产业类型',
        area_code_list: areaCodeList, // 地区代码数组
        industrial_list: chainData?.code ? [chainData.code] : [], // 产业代码数组
        ...handleData(moreFilterParams),
        isFilter: type
      };

      console.log('DropDownMenu 返回数据:', requestData);
      this.triggerEvent('submit', requestData);
      this.closeHyFilter();
    },

    // 应用外部状态
    applyExternalState(externalState) {
      if (!externalState) return;

      console.log('DropDownMenu applyExternalState:', externalState);

      // 设置回填标志，防止触发observer
      this.setData({isBackfilling: true});

      const {
        regionData,
        regionDataList,
        industrial_list,
        classic_industry_code_list,
        filterParams
      } = externalState;

      let {dropDownMenuTitle} = this.properties;
      let newDropDownMenuTitle = [...dropDownMenuTitle];

      // 回显地区数据 - 支持多个地区
      if (regionDataList && regionDataList.length > 0) {
        // 多个地区的情况
        this.setData({
          regionDataList: regionDataList,
          district_val: true
        });

        // 显示地区名称 - 使用原来的地区名称显示逻辑
        const regionName = getNameFromPop(regionDataList);
        newDropDownMenuTitle[0] = regionName || '全国';

        console.log('设置多个地区数据:', regionDataList, 'district_val: true');
      } else if (regionData && regionData.code) {
        // 单个地区的情况（兼容性处理）
        if (regionData.code === 'All' || regionData.code === '1000000') {
          // 全国情况，不高亮
          this.setData({
            regionData: {},
            district_val: false
          });
          newDropDownMenuTitle[0] = '全国';
        } else {
          // 其他地区，高亮显示
          this.setData({
            regionData: regionData,
            district_val: true
          });
          newDropDownMenuTitle[0] = regionData.name || '全国';
        }
        console.log(
          '设置地区数据:',
          regionData,
          'district_val:',
          regionData.code !== 'All' && regionData.code !== '1000000'
        );
      }

      // 回显产业数据
      const chainItem = industrial_list || classic_industry_code_list;
      if (chainItem && chainItem.code) {
        this.setData({
          chainData: chainItem,
          source_val: true,
          selectChainVal: chainItem.code
        });
        newDropDownMenuTitle[1] = chainItem.name || '产业类型';
        console.log('设置产业数据:', chainItem, 'source_val: true');
      }

      // 更新菜单标题
      this.setData({
        dropDownMenuTitle: newDropDownMenuTitle
      });

      // 回显更多筛选参数
      if (filterParams) {
        // 排除一些不需要的字段
        const {area_code_list, industry_code_list, ...moreParams} =
          filterParams;

        // 确保有有效的筛选参数才设置
        if (Object.keys(moreParams).length > 0) {
          this.setData({
            moreFilterParams: moreParams,
            filter_val: true // 设置更多筛选高亮
          });

          // 调用 ConfigurableForm 的回填方法
          setTimeout(() => {
            const configurableForm = this.selectComponent('#s-hunt');
            if (configurableForm && configurableForm.setBackfillData) {
              configurableForm.setBackfillData(moreParams);
            }
          }, 100);

          console.log('设置筛选参数:', moreParams, 'filter_val: true');
        }
      }

      this.setData({isBackfilling: false});
    }
  }
});
