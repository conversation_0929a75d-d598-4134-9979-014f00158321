// companyPackage/pages/industryChain/chainList/component/ChainSelect/ChainSelect.js
import {chain} from '../../../service/api';
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    visible: {
      type: Boolean,
      value: false
    },
    // 弹窗位置(top/bottom)
    position: {
      type: String,
      value: 'top'
    },
    // 默认选中，通过code匹配
    defaultVal: {
      type: String,
      value: '',
      observer(val) {
        const {allData} = this.data;
        this.setDefaultSelect(val, allData);
      }
    },
    // 所有数据
    /** 数据示例：
     * [{
     *  code: '',
     *  name: '',
     *  children: [{
     *   code: '',
     *   name: '',
     *   parent_code: ''
     *  }]
     * }]
     *
     */
    allData: {
      type: Array,
      value: []
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    activeChildList: [] // 处于活动状态的子级列表
  },
  /**
   * 组件的方法列表
   */
  methods: {
    // 设置默认选中
    setDefaultSelect(code, data) {
      for (let item of data) {
        if (item.code === code) {
          const {allData} = this.data;
          item.active = true;
          // 获取选中父级的下标
          const parentInd = allData
            .findIndex(val => val.code === item.parent_code)
            .toString();
          // 如果默认选中的是子级
          if (parentInd !== '-1') {
            // 将对应父级设置为活动状态
            allData[parentInd].active = true;
            this.setData({
              allData,
              activeChildList: JSON.parse(JSON.stringify(data))
            });
          } else {
            // 如果默认选中的父级，将子级第一个设置为活动状态
            // item.children[0].active = true;
            const tempArr = JSON.parse(JSON.stringify(item.children));
            tempArr[0].active = true;
            this.setData({allData: data, activeChildList: tempArr});
          }
        } else {
          item.children && this.setDefaultSelect(code, item.children);
        }
      }
    },
    // 选中切换
    handleChange({
      currentTarget: {
        dataset: {select, parentind = ''}
      }
    }) {
      let {allData, activeChildList} = this.data;
      if (parentind.toString()) {
        allData = allData.map(item => {
          item.active = false;
          if (item.code === select.code) {
            item.active = true;
            activeChildList = JSON.parse(JSON.stringify(item.children));
            activeChildList[0].active = true;
          }
          return item;
        });
        this.setData({allData, activeChildList});
      } else {
        activeChildList = activeChildList.map(item => {
          item.active = item.code === select.code ? true : false;
          return item;
        });
        this.setData({activeChildList});
      }
      // this.setData({ visible: false });
      this.triggerEvent(
        'select',
        activeChildList.filter(item => item.active)
      );
    }
  }
});
